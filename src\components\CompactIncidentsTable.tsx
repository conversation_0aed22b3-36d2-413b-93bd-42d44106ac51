import React from "react";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye, FileUp, ClipboardList, AlertCircle } from "lucide-react";

interface CompactIncidentsTableProps {
  incidents: any[];
  handleView: (incident: any) => void;
  handleEdit: (id: string) => void;
  handleDelete: (id: string) => void;
  getIncidentTypeLabel: (value: string) => string;
  getIncidentCategoryLabel: (value: string) => string;
  getStatusLabel: (status: string) => string;
  handleAttachImage: (incidentId: string) => void;
  handleInvestigation: (incidentId: string, status: string) => void;
}

// Helper function to determine workflow stage based on status
const determineWorkflowStage = (status: string): string => {
  switch (status) {
    case 'draft':
      return 'Initial Report';
    case 'submitted':
      return 'Preliminary Analysis in Progress';
    case 'under-review':
      return 'Supplementary information in Progress';
    case 'investigation':
      return 'Investigation';
    case 'closed':
      return 'Preliminary Analysis Completed';
    default:
      return 'Initial Report';
  }
};

const CompactIncidentsTable = ({
  incidents,
  handleView,
  handleEdit,
  handleDelete,
  getIncidentTypeLabel,
  getIncidentCategoryLabel,
  getStatusLabel,
  handleAttachImage,
  handleInvestigation,
}: CompactIncidentsTableProps) => {
  // Use all incidents including drafts
  const filteredIncidents = incidents;

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead className="w-[100px]">Incident ID</TableHead>
            <TableHead className="w-[150px]">Incident Date</TableHead>
            <TableHead className="w-[200px]">Incident Title</TableHead>
            <TableHead className="w-[120px]">Category</TableHead>
            <TableHead className="w-[150px]">Classification</TableHead>
            <TableHead className="w-[150px]">Impact Classification</TableHead>
            <TableHead className="w-[120px]">Incident Status</TableHead>
            <TableHead className="w-[120px]">Stage</TableHead>
            <TableHead className="w-[150px]">Incident Owner</TableHead>
            <TableHead className="w-[150px]">Reported By</TableHead>
            <TableHead className="w-[150px]">Reviewed By</TableHead>
            <TableHead className="w-[120px] text-center">Actions Taken</TableHead>
            <TableHead className="w-[150px] text-center">Investigation Status</TableHead>
            <TableHead className="w-[120px] text-center">Attachment</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredIncidents.length > 0 ? (
            filteredIncidents.map((incident) => (
              <TableRow key={incident.id} className="hover:bg-muted/50">
                <TableCell className="font-medium">
                  <Button
                    variant="link"
                    className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                    onClick={() => handleView(incident)}
                  >
                    {incident.maskId || incident.id}
                  </Button>
                </TableCell>
                <TableCell>{format(incident.incidentDate, "do MMM yyyy")}</TableCell>
                <TableCell>
                  <div className="max-w-[180px] truncate font-medium" title={incident.incidentTitle || incident.description}>
                    {incident.incidentTitle || (incident.incidentType === "fall" ? "Fall Incident" : incident.description)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    at {incident.workplaceActivity || "Site"} {incident.incidentLocation || ""}
                  </div>
                </TableCell>
                <TableCell>{getIncidentCategoryLabel(incident.incidentCategory)}</TableCell>
                <TableCell>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {incident.classification || "Not Classified"}
                  </span>
                </TableCell>
                <TableCell>
                  {incident.impactAssessment === "Yes" ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Level 4 (FAI - First Aid Incident)
                    </span>
                  ) : incident.impactAssessment === "No, but it could have" ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Near Miss
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      No Impact
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {getStatusLabel(incident.status)}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    {incident.stage ||
                     (incident.workflowStage === "initial" ? "Initial Report" :
                     incident.workflowStage === "review" ? "Reviewer" :
                     incident.workflowStage === "investigation" ? "Investigation" :
                     determineWorkflowStage(incident.status))}
                  </span>
                </TableCell>
                <TableCell>{incident.incidentOwner}</TableCell>
                <TableCell>{incident.reportedBy}</TableCell>
                <TableCell>{incident.reviewedBy}</TableCell>
                <TableCell className="text-center">
                  <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium ${incident.actionsTakenCount === 0 ? 'bg-gray-100 text-gray-800' : incident.actionsTakenCount < incident.actionsTakenTotal ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                    {incident.actionsTakenCount}/{incident.actionsTakenTotal}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full"
                          onClick={() => handleInvestigation(incident.id, incident.investigationStatus)}
                        >
                          <div className="relative">
                            <ClipboardList className="h-4 w-4" />
                            {incident.investigationStatus === "open" && (
                              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-yellow-500"></span>
                            )}
                            {incident.investigationStatus === "in-progress" && (
                              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-500"></span>
                            )}
                            {incident.investigationStatus === "closed" && (
                              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-green-500"></span>
                            )}
                          </div>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          Investigation Status:
                          {incident.investigationStatus === "open" && " Open"}
                          {incident.investigationStatus === "in-progress" && " In Progress"}
                          {incident.investigationStatus === "closed" && " Closed"}
                        </p>
                        <p className="text-xs text-muted-foreground">Click to assign lead investigator</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex items-center justify-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleView(incident)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>View Attachments</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleAttachImage(incident.id)}
                          >
                            <FileUp className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Add Attachment</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={13} className="text-center py-8 text-gray-500">
                No submitted incidents found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default CompactIncidentsTable;
