
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Eye, Edit, Trash2, Plus, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import CompactIncidentsTable from "@/components/CompactIncidentsTable";
import NewIncidentDialog from "@/components/NewIncidentDialog";
import IncidentActionDialog from "@/components/IncidentActionDialog";
import IncidentInvestigationView from "@/components/IncidentInvestigationView";
import InvestigationStatusDialog from "@/components/InvestigationStatusDialog";
import { useUser } from "@/contexts/UserContext";
import { useIncidents } from "@/contexts/IncidentContext";

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";

// No need to import mockIncidents anymore

const Incidents = () => {
  const {
    role,
    userData,
    isLoading,
    error,
    fetchUserData,
    availableRoles,
    hasIncidentReporter,
    hasIncidentReviewer
  } = useUser();
  const { incidents, updateIncident, addIncident, refreshIncidents, isLoading: incidentsLoading, error: incidentsError } = useIncidents();
  // const [incidents, setIncidents] = useState([]);
  const [viewIncident, setViewIncident] = useState<any>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isNewIncidentDialogOpen, setIsNewIncidentDialogOpen] = useState(false);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInvestigationViewOpen, setIsInvestigationViewOpen] = useState(false);
  const [isInvestigationStatusDialogOpen, setIsInvestigationStatusDialogOpen] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<any>(null);

  // Helper function to get label from value
  const getIncidentTypeLabel = (value: string) => {
    const types: Record<string, string> = {
      safety: "Safety",
      environmental: "Environmental",
      nearMiss: "Near Miss",
      propertyDamage: "Property Damage",
      security: "Security",
      quality: "Quality",
    };
    return types[value] || value;
  };

  const getIncidentCategoryLabel = (value: string) => {
    const categories: Record<string, string> = {
      fire: "Fire",
      slip: "Slip",
      fall: "Fall",
      electrical: "Electrical Hazard",
      chemical: "Chemical Spill",
      vehicle: "Vehicle Accident",
      machinery: "Machinery",
      tool: "Tool Related",
      ergonomic: "Ergonomic",
      other: "Other",
    };
    return categories[value] || value;
  };

  const handleView = (incident: any) => {
    // For incidents under investigation, use the investigation view
    if (incident.status === "investigation") {
      setSelectedIncident(incident);
      setIsInvestigationViewOpen(true);
    } else {
      // For other incidents, use the regular view
      setViewIncident(incident);
      setIsViewDialogOpen(true);
    }
  };

  const handleEdit = (id: string) => {
    toast.info(`Editing incident ${id}`, {
      description: "This would navigate to the edit form in a real application.",
    });
  };

  const handleDelete = (id: string) => {
    toast.success("Incident deleted", {
      description: `Incident ${id} has been deleted.`,
    });
  };

  const handleAttachImage = (incidentId: string) => {
    toast.success(`Attach image functionality for incident ${incidentId}`, {
      description: "In a real app, this would open a file picker and upload the image.",
    });
  };

  const handleInvestigation = (incidentId: string, currentStatus: string) => {
    // Find the incident and open the investigation status dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsInvestigationStatusDialogOpen(true);
    }
  };

  // Handle saving investigation assignment
  const handleInvestigationAssignment = (data: { leadInvestigator: string; remarks: string }) => {
    if (!selectedIncident) return;

    // Update the incident with lead investigator and remarks
    const updatedIncident = {
      ...selectedIncident,
      leadInvestigator: data.leadInvestigator,
      investigationRemarks: data.remarks,
      investigationStatus: 'in-progress' // Set status to in-progress when assigned
    };

    updateIncident(selectedIncident.id, updatedIncident);

    toast.success(`Investigation assigned for incident ${selectedIncident.id}`, {
      description: `Lead investigator assigned successfully`,
    });
  };

  const handleAction = (incidentId: string) => {
    // Find the incident and open the action dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsActionDialogOpen(true);
    }
  };

  // Handle the completion of an action from the dialog
  const handleActionComplete = (updatedIncident: any) => {
    // Update the incident using the context
    updateIncident(updatedIncident.id, updatedIncident);
    setSelectedIncident(null);
  };

  // Filter incidents based on tab selection
  const myActionIncidents = incidents.filter(
    (incident) => incident.assignedTo === "current-user" && incident.requiresAction === true
  );
  const underReviewIncidents = incidents.filter(
    (incident) => incident.status === "under-review"
  );
  const underInvestigationIncidents = incidents.filter(
    (incident) => incident.status === "investigation"
  );

  // Count incidents for each tab
  const myActionCount = myActionIncidents.length;
  const allIncidentsCount = incidents.length;
  const underReviewCount = underReviewIncidents.length;
  const underInvestigationCount = underInvestigationIncidents.length;

  // Map status to display label with proper casing
  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      open: "Reviewed",
      "under-review": "Under Review",
      investigation: "Under Investigation",
      submitted: "Reported",
      closed: "Closed",
    };
    return labels[status] || status;
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold">Incident Management</h1>
        {role === 'reporter' && (
          <Button
            className="bg-primary hover:bg-primary/90"
            onClick={() => setIsNewIncidentDialogOpen(true)}
          >
            <Plus className="mr-2" />
            New Incident
          </Button>
        )}
      </div>

      {/* API Data Display Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            API Connection Status
            <Button
              variant="outline"
              size="sm"
              onClick={fetchUserData}
              disabled={isLoading}
            >
              {isLoading ? "Loading..." : "Refresh User"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshIncidents}
              disabled={incidentsLoading}
            >
              {incidentsLoading ? "Loading..." : "Refresh Incidents"}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="text-blue-600">Loading user data...</div>
          )}
          {error && (
            <div className="text-red-600 mb-2">
              <strong>Error:</strong> {error}
            </div>
          )}
          {incidentsLoading && (
            <div className="text-blue-600">Loading incidents data...</div>
          )}
          {incidentsError && (
            <div className="text-red-600 mb-2">
              <strong>Incidents Error:</strong> {incidentsError}
            </div>
          )}
          {userData && (
            <div className="space-y-4">
              <div className="text-green-600 font-medium">✅ API Connection Successful!</div>

              {/* Incidents API Status */}
              <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 className="font-medium text-purple-800 mb-3">Incidents API Integration Status:</h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Incidents Loaded:</span>
                    <span className="text-purple-600">
                      {incidents.length} incidents
                    </span>
                  </div>

                  {incidentsError && (
                    <div className="bg-yellow-50 p-3 rounded border border-yellow-200">
                      <div className="text-yellow-800 text-sm">
                        <strong>⚠️ API Status:</strong> {incidentsError}
                      </div>
                      <div className="text-yellow-700 text-xs mt-1">
                        Currently using mock data as fallback. The system tried multiple endpoints:
                        /incidents, /get-incidents, /new-report-incidents, /report-incidents, etc.
                      </div>
                    </div>
                  )}

                  {!incidentsError && (
                    <div className="text-green-600 text-sm">
                      ✅ Successfully connected to incidents API
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    {incidentsLoading ? "🔄 Loading incidents from API..." :
                     incidentsError ? "📋 Using mock data (API endpoints not available)" :
                     "✅ Incidents loaded from API"}
                  </div>

                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    <strong>Note:</strong> The /new-report-incidents endpoint is used for creating incidents.
                    To populate tables with real data, the API server needs to provide a GET endpoint
                    for retrieving incidents (e.g., GET /incidents or GET /get-incidents).
                  </div>
                </div>
              </div>

              {/* Role Analysis Section */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-3">User Role Analysis:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Incident Reporter:</span>
                      <span className={hasIncidentReporter ? 'text-green-600' : 'text-red-600'}>
                        {hasIncidentReporter ? '✅ Available' : '❌ Not Available'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Incident Reviewer:</span>
                      <span className={hasIncidentReviewer ? 'text-green-600' : 'text-red-600'}>
                        {hasIncidentReviewer ? '✅ Available' : '❌ Not Available'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Available Roles:</span>
                      <span className="text-blue-700">
                        {availableRoles.length > 0 ? availableRoles.join(', ') : 'None'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Current Role:</span>
                    <div className="mt-1 px-3 py-1 bg-white rounded border text-sm">
                      {role}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Full User Data from API:</h4>
                <pre className="text-sm overflow-auto max-h-64">
                  {JSON.stringify(userData, null, 2)}
                </pre>
              </div>
            </div>
          )}
          {!userData && !isLoading && !error && (
            <div className="text-gray-600">No user data available</div>
          )}
        </CardContent>
      </Card>

      {/* New Incident Dialog */}
      <NewIncidentDialog
        open={isNewIncidentDialogOpen}
        onOpenChange={setIsNewIncidentDialogOpen}
        onIncidentCreated={(newIncident) => {
          // Add the new incident using the context
          addIncident(newIncident);
        }}
      />

      {/* Incident Action Dialog */}
      <IncidentActionDialog
        open={isActionDialogOpen}
        onOpenChange={setIsActionDialogOpen}
        incident={selectedIncident}
        onActionComplete={handleActionComplete}
      />

      {/* Incident Investigation View */}
      <IncidentInvestigationView
        open={isInvestigationViewOpen}
        onOpenChange={setIsInvestigationViewOpen}
        incident={selectedIncident}
      />

      {/* Investigation Status Dialog */}
      <InvestigationStatusDialog
        open={isInvestigationStatusDialogOpen}
        onOpenChange={setIsInvestigationStatusDialogOpen}
        incident={selectedIncident}
        onSave={handleInvestigationAssignment}
        onStartInvestigation={(data) => {
          // Handle starting comprehensive investigation
          console.log("Starting comprehensive investigation with data:", data);

          // Update the incident with lead investigator and start comprehensive investigation
          if (selectedIncident) {
            const updatedIncident = {
              ...selectedIncident,
              leadInvestigator: data.leadInvestigator,
              investigationRemarks: data.remarks,
              status: 'investigation' as const, // Change status to investigation
              investigationStatus: 'in-progress' as const,
              workflowStage: 'investigation' as const,
              stage: 'Investigation in Progress',
              requiresAction: false, // Remove from My Actions since it's now under investigation
              comprehensiveInvestigationStarted: true,
              comprehensiveInvestigationStartedAt: new Date(),
              comprehensiveInvestigationStartedBy: data.leadInvestigator,
            };

            // Update the incident in the context
            updateIncident(selectedIncident.id, updatedIncident);

            // Clear the selected incident
            setSelectedIncident(null);

            // Show success message
            toast.success("Comprehensive Investigation Started!", {
              description: `Lead investigator ${data.leadInvestigator} has been assigned and comprehensive investigation has started.`,
            });
          }
        }}
      />

      <Tabs defaultValue="my-actions" className="w-full">
        <div className="border-b mb-6">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="my-actions" className="relative px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:shadow-none">
              My Actions <span className="ml-1 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">{myActionCount}</span>
            </TabsTrigger>
            <TabsTrigger value="all-incidents" className="relative px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:shadow-none">
              All Incidents <span className="ml-1 bg-muted text-muted-foreground rounded-full px-2 py-0.5 text-xs">{allIncidentsCount}</span>
            </TabsTrigger>
            <TabsTrigger value="under-review" className="relative px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:shadow-none animate-in fade-in duration-300">
              Under Review <span className="ml-1 bg-amber-100 text-amber-800 rounded-full px-2 py-0.5 text-xs">{underReviewCount}</span>
            </TabsTrigger>
            <TabsTrigger value="under-investigation" className="relative px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:shadow-none">
              Under Investigation <span className="ml-1 bg-muted text-muted-foreground rounded-full px-2 py-0.5 text-xs">{underInvestigationCount}</span>
            </TabsTrigger>
            <TabsTrigger value="to-be-investigated" className="relative px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:shadow-none">
              To be Investigated by You <span className="ml-1 bg-muted text-muted-foreground rounded-full px-2 py-0.5 text-xs">0</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="my-actions">
          <div className="rounded-md border">
            {myActionIncidents.length > 0 ? (
              <IncidentsTable
                incidents={myActionIncidents}
                handleView={handleView}
                handleEdit={handleEdit}
                handleDelete={handleDelete}
                getIncidentTypeLabel={getIncidentTypeLabel}
                getIncidentCategoryLabel={getIncidentCategoryLabel}
                handleAction={handleAction}
              />
            ) : (
              <div className="p-8 text-center">
                <h3 className="text-lg font-medium mb-2">No actions required</h3>
                <p className="text-muted-foreground">When new incidents are reported, they will appear here for your review.</p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="all-incidents">
          <div className="rounded-md border">
            <CompactIncidentsTable
              incidents={incidents}
              handleView={handleView}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              getIncidentTypeLabel={getIncidentTypeLabel}
              getIncidentCategoryLabel={getIncidentCategoryLabel}
              getStatusLabel={getStatusLabel}
              handleAttachImage={handleAttachImage}
              handleInvestigation={handleInvestigation}
            />
          </div>
        </TabsContent>

        <TabsContent value="under-review" className="animate-in fade-in duration-300">
          <div className="rounded-md border">
            <IncidentsTable
              incidents={underReviewIncidents}
              handleView={handleView}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              getIncidentTypeLabel={getIncidentTypeLabel}
              getIncidentCategoryLabel={getIncidentCategoryLabel}
              handleAction={handleAction}
            />
          </div>
        </TabsContent>

        <TabsContent value="under-investigation">
          <div className="rounded-md border">
            <IncidentsTable
              incidents={underInvestigationIncidents}
              handleView={handleView}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              getIncidentTypeLabel={getIncidentTypeLabel}
              getIncidentCategoryLabel={getIncidentCategoryLabel}
              disableEditing={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="to-be-investigated">
          <div className="rounded-md border">
            <IncidentsTable
              incidents={[]}
              handleView={handleView}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              getIncidentTypeLabel={getIncidentTypeLabel}
              getIncidentCategoryLabel={getIncidentCategoryLabel}
            />
          </div>
        </TabsContent>
        </Tabs>

        {/* View Incident Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl">
                Incident Report: {viewIncident?.id}
              </DialogTitle>
              <DialogDescription>
                Reported on{" "}
                {viewIncident && format(viewIncident.reportedAt, "PPP pp")}
              </DialogDescription>
            </DialogHeader>

            {viewIncident && (
              <div className="grid gap-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-b pb-4">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Incident Information</h3>

                    <div>
                      <h4 className="font-medium text-gray-700">Date & Time</h4>
                      <p>{format(viewIncident.incidentDate, "PPPP")} at {viewIncident.incidentTime}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700">Type</h4>
                      <p>{getIncidentTypeLabel(viewIncident.incidentType)}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700">Category</h4>
                      <p>{getIncidentCategoryLabel(viewIncident.incidentCategory)}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Incident Context</h3>

                    <div>
                      <h4 className="font-medium text-gray-700">Location</h4>
                      <p>{viewIncident.country}{viewIncident.city ? `, ${viewIncident.city}` : ""}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700">Workplace Activity</h4>
                      <p>{viewIncident.workplaceActivity || "Not specified"}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700">Risk Categories</h4>
                      {viewIncident.riskCategories && viewIncident.riskCategories.length > 0 ? (
                        <ul className="list-disc pl-5">
                          {viewIncident.riskCategories.map((risk: string, index: number) => (
                            <li key={index}>{risk}</li>
                          ))}
                        </ul>
                      ) : (
                        <p>None specified</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-b pb-4">
                  <h3 className="font-semibold text-primary text-lg mb-2">Description</h3>
                  <p className="text-gray-700 whitespace-pre-wrap">{viewIncident.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-b pb-4">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Property Damage</h3>
                    <p>{viewIncident.propertyDamage ? "Yes" : "No"}</p>
                    {viewIncident.propertyDamage && viewIncident.propertyDamageDetails && (
                      <div>
                        <h4 className="font-medium text-gray-700">Details</h4>
                        <p>{viewIncident.propertyDamageDetails}</p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Impact Assessment</h3>
                    <div>
                      <h4 className="font-medium text-gray-700">Assessment</h4>
                      <p>{viewIncident.impactAssessment || "Not assessed"}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700">Escalation Level</h4>
                      <p>{viewIncident.escalationLevel || "Not specified"}</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Responsibility</h3>
                    <div>
                      <h4 className="font-medium text-gray-700">Incident Owner</h4>
                      <p>{viewIncident.incidentOwner}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Regulatory</h3>
                    <div>
                      <h4 className="font-medium text-gray-700">Reportable to Authorities</h4>
                      <p>{viewIncident.reportableToAuthorities ? "Yes" : "No"}</p>
                      {viewIncident.reportableToAuthorities && viewIncident.authorityReportDetails && (
                        <div>
                          <h4 className="font-medium text-gray-700">Details</h4>
                          <p>{viewIncident.authorityReportDetails}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-primary text-lg">Status</h3>
                    <p className="capitalize">{viewIncident.status || "Open"}</p>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
  );
};

// Reusing the existing general IncidentsTable component for simple tabs
interface IncidentsTableProps {
  incidents: any[];
  handleView: (incident: any) => void;
  handleEdit: (id: string) => void;
  handleDelete: (id: string) => void;
  getIncidentTypeLabel: (value: string) => string;
  getIncidentCategoryLabel: (value: string) => string;
  handleAction?: (id: string) => void;
  disableEditing?: boolean;
}

const IncidentsTable = ({
  incidents,
  handleView,
  handleEdit,
  handleDelete,
  getIncidentTypeLabel,
  getIncidentCategoryLabel,
  handleAction,
  disableEditing = false,
}: IncidentsTableProps) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableCaption>List of reported incidents</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Date & Time</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Owner</TableHead>
            <TableHead>Reported</TableHead>
            <TableHead>Incident Status</TableHead>
            <TableHead>Stage</TableHead>
            <TableHead>Action Required</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
      <TableBody>
        {incidents.length > 0 ? (
          incidents.map((incident) => (
            <TableRow key={incident.id}>
              <TableCell className="font-medium">
                <Button
                  variant="link"
                  className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                  onClick={() => handleView(incident)}
                >
                  {incident.id}
                </Button>
              </TableCell>
              <TableCell>
                {format(incident.incidentDate, "MMM d, yyyy")} at{" "}
                {incident.incidentTime}
              </TableCell>
              <TableCell>{getIncidentTypeLabel(incident.incidentType)}</TableCell>
              <TableCell>{getIncidentCategoryLabel(incident.incidentCategory)}</TableCell>
              <TableCell>{incident.incidentOwner}</TableCell>
              <TableCell>{format(incident.reportedAt, "MMM d, yyyy")}</TableCell>
              <TableCell>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {getStatusLabel(incident.status)}
                </span>
              </TableCell>
              <TableCell>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {incident.stage ||
                   (incident.status === "under-review" ? "Supplementary information in Progress" :
                   incident.status === "investigation" ? "Investigation" :
                   incident.status === "submitted" ? "Preliminary Analysis in Progress" :
                   incident.status === "closed" ? "Preliminary Analysis Completed" : "Initial Report")}
                </span>
              </TableCell>
              <TableCell>
                {incident.requiresAction && incident.actionType && (
                  <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
                    {incident.actionType === "review" ? "Review Required" : incident.actionType}
                  </span>
                )}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  {incident.requiresAction && handleAction && (
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() => handleAction(incident.id)}
                      title="Take Action"
                    >
                      Take Action
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleView(incident)}
                    title="View"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {!disableEditing && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(incident.id)}
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:border-red-300"
                        onClick={() => handleDelete(incident.id)}
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={10} className="text-center py-8 text-gray-500">
              No incidents reported yet
            </TableCell>
          </TableRow>
        )}
      </TableBody>
      </Table>
    </div>
  );
};

interface AllIncidentsTableProps {
  incidents: any[];
  handleView: (incident: any) => void;
  handleEdit: (id: string) => void;
  handleDelete: (id: string) => void;
  getIncidentCategoryLabel: (value: string) => string;
  getStatusLabel: (status: string) => string;
  handleAttachImage: (incidentId: string) => void;
}

const AllIncidentsTable = ({
  incidents,
  handleView,
  handleEdit,
  handleDelete,
  getIncidentCategoryLabel,
  getStatusLabel,
  handleAttachImage,
}: AllIncidentsTableProps) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableCaption>List of all reported incidents with details</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Incident Title</TableHead>
            <TableHead>Incident Date</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Impact Classification</TableHead>
            <TableHead>Incident Status</TableHead>
            <TableHead>Stage</TableHead>
            <TableHead>Incident Owner</TableHead>
            <TableHead>Reported By</TableHead>
            <TableHead>Reviewed By</TableHead>
            <TableHead>Actions Taken</TableHead>
            <TableHead>Investigation Status</TableHead>
            <TableHead>Attachment</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
      <TableBody>
        {incidents.length > 0 ? (
          incidents.map((incident) => (
            <TableRow key={incident.id}>
              <TableCell className="font-medium">
                <Button
                  variant="link"
                  className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                  onClick={() => handleView(incident)}
                >
                  {incident.id}
                </Button>
              </TableCell>
              <TableCell title={incident.incidentTitle || incident.description}>
                {incident.incidentTitle
                  ? (incident.incidentTitle.length > 35
                      ? `${incident.incidentTitle.substring(0, 35)}...`
                      : incident.incidentTitle)
                  : (incident.description.length > 35
                      ? `${incident.description.substring(0, 35)}...`
                      : incident.description)}
              </TableCell>
              <TableCell>{format(incident.incidentDate, "PPP")}</TableCell>
              <TableCell>{getIncidentCategoryLabel(incident.incidentCategory)}</TableCell>
              <TableCell>{incident.impactAssessment || "N/A"}</TableCell>
              <TableCell>{getStatusLabel(incident.status)}</TableCell>
              <TableCell>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {incident.stage ||
                   (incident.status === "under-review" ? "Supplementary information in Progress" :
                   incident.status === "investigation" ? "Investigation" :
                   incident.status === "submitted" ? "Preliminary Analysis in Progress" :
                   incident.status === "closed" ? "Preliminary Analysis Completed" : "Initial Report")}
                </span>
              </TableCell>
              <TableCell>{incident.incidentOwner || "N/A"}</TableCell>
              <TableCell>{incident.reportedBy || "N/A"}</TableCell>
              <TableCell>{incident.reviewedBy || "N/A"}</TableCell>
              <TableCell>{`${incident.actionsTakenCount || 0}/${incident.actionsTakenTotal || 0}`}</TableCell>
              <TableCell className="text-center">
                {incident.investigationStatus === "open" && (
                  <AlertCircle className="text-yellow-500 mx-auto" title="Open Investigation" />
                )}
                {incident.investigationStatus === "in-progress" && (
                  <AlertCircle className="text-blue-500 mx-auto" title="Investigation In Progress" />
                )}
                {incident.investigationStatus === "closed" && (
                  <AlertCircle className="text-green-500 mx-auto" title="Investigation Closed" />
                )}
              </TableCell>
              <TableCell className="text-center">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleAttachImage(incident.id)}
                  title="Attach Image"
                >
                  📎
                </Button>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleView(incident)}
                    title="View"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {incident.status !== "investigation" && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(incident.id)}
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:border-red-300"
                        onClick={() => handleDelete(incident.id)}
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={14} className="text-center py-8 text-gray-500">
              No incidents reported yet
            </TableCell>
          </TableRow>
        )}
      </TableBody>
      </Table>
    </div>
  );
};

export default Incidents;

