import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService } from '@/lib/utils';
import { toast } from 'sonner';

const DeleteIncidentTest = () => {
  const [incidentId, setIncidentId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDeleteIncident = async () => {
    if (!incidentId.trim()) {
      toast.error('Please enter an incident ID');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`🗑️ Testing delete for incident ID: ${incidentId}`);
      const response = await ApiService.deleteIncident(incidentId);
      console.log('✅ Delete test successful:', response);
      setResult(response);
      toast.success('Incident deleted successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Delete test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Delete failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Delete Incident API Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            DELETE https://dev.stt-user.acuizen.com/new-report-incidents/{'{id}'}
          </code>
        </div>

        <div className="space-y-2">
          <label htmlFor="incidentId" className="font-medium">
            Incident ID to Delete:
          </label>
          <div className="flex gap-2">
            <Input
              id="incidentId"
              type="text"
              placeholder="Enter incident ID (e.g., 123)"
              value={incidentId}
              onChange={(e) => setIncidentId(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={testDeleteIncident} 
              disabled={isLoading || !incidentId.trim()}
              variant="destructive"
            >
              {isLoading ? 'Deleting...' : 'Delete Incident'}
            </Button>
          </div>
        </div>

        <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded border border-amber-200">
          ⚠️ <strong>Warning:</strong> This will permanently delete the incident from the server. 
          Make sure you're using a test incident ID.
        </div>

        {isLoading && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="text-blue-700">🔄 Deleting incident...</div>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded">
            <div className="text-red-700 font-medium">❌ Error:</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
          </div>
        )}

        {result && (
          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <div className="text-green-700 font-medium mb-2">✅ Success! Delete Response:</div>
            <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-96">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        {!result && !isLoading && !error && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Enter an incident ID and click "Delete Incident" to test the API</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DeleteIncidentTest;
