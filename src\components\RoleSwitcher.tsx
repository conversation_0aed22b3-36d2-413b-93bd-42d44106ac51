import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useUser } from "@/contexts/UserContext";
import { ChevronDown, ClipboardList, Shield } from "lucide-react";

const RoleSwitcher = () => {
  const {
    role,
    setRole,
    userName,
    userTitle,
    availableRoles,
    hasIncidentReporter,
    hasIncidentReviewer,
    userData,
    isLoading
  } = useUser();
  const navigate = useNavigate();

  const handleRoleChange = (newRole: "reporter" | "reviewer") => {
    setRole(newRole);

    // Navigate to the appropriate dashboard
    switch (newRole) {
      case "reporter":
        navigate("/reporter");
        break;
      case "reviewer":
        navigate("/reviewer");
        break;
    }
  };

  // Get the current role icon and label
  const getRoleIcon = () => {
    switch (role) {
      case "reporter":
        return <ClipboardList className="h-4 w-4 mr-2" />;
      case "reviewer":
        return <Shield className="h-4 w-4 mr-2" />;
      default:
        return <ClipboardList className="h-4 w-4 mr-2" />;
    }
  };

  const getRoleLabel = () => {
    switch (role) {
      case "reporter":
        return "Reporter";
      case "reviewer":
        return "Reviewer";
      default:
        return "Reporter";
    }
  };

  // Don't show anything if user data is still loading
  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground">
        Loading roles...
      </div>
    );
  }

  // Don't show dropdown if user has no incident-related roles
  if (!hasIncidentReporter && !hasIncidentReviewer) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground">
        No incident roles assigned
      </div>
    );
  }

  // If user has only one role, show it without dropdown
  if (availableRoles.length === 1) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-muted rounded-md">
        {getRoleIcon()}
        <span className="font-medium">{getRoleLabel()}</span>
      </div>
    );
  }

  // Show dropdown if user has multiple roles
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 transition-all duration-300 hover:bg-muted">
          {getRoleIcon()}
          <span>{getRoleLabel()}</span>
          <ChevronDown className="h-4 w-4 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56 animate-in zoom-in-95 duration-100">
        <DropdownMenuLabel>Switch Role</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {hasIncidentReporter && (
          <DropdownMenuItem
            className={`flex items-center cursor-pointer ${role === "reporter" ? "bg-muted" : ""}`}
            onClick={() => handleRoleChange("reporter")}
          >
            <ClipboardList className="h-4 w-4 mr-2" />
            <div className="flex flex-col">
              <span>Reporter</span>
              <span className="text-xs text-muted-foreground">Report new incidents</span>
            </div>
          </DropdownMenuItem>
        )}
        {hasIncidentReviewer && (
          <DropdownMenuItem
            className={`flex items-center cursor-pointer ${role === "reviewer" ? "bg-muted" : ""}`}
            onClick={() => handleRoleChange("reviewer")}
          >
            <Shield className="h-4 w-4 mr-2" />
            <div className="flex flex-col">
              <span>Reviewer</span>
              <span className="text-xs text-muted-foreground">Review and process incidents</span>
            </div>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default RoleSwitcher;
