import React, { useState } from "react";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, Trash2, Users, Clock, FileText, Wrench, Package, Cloud, Settings, Target, CheckCircle, Upload, Image } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Define the validation schema for the comprehensive investigation form
const comprehensiveInvestigationSchema = z.object({
  // Investigation Team
  investigationTeam: z.object({
    leadInvestigator: z.string().min(1, "Lead investigator is required"),
    teamMembers: z.array(z.object({
      name: z.string(),
      designation: z.string(),
    })).default([]),
    investigationStartDate: z.date().optional(),
    investigationEndDate: z.date().optional(),
    purposeOfInvestigation: z.string().optional(),
    consequenceOfInterest: z.string().optional(),
    timeframeOfInterest: z.string().optional(),
    peopleOfInterest: z.string().optional(),
    equipmentOfInterest: z.string().optional(),
    activitiesOfInterest: z.string().optional(),
    geographicalBoundariesOfInterest: z.string().optional(),
  }).optional(),

  // Sequence of Events
  sequenceOfEvents: z.object({
    description: z.string().optional(),
    incidentDate: z.date().optional(),
    incidentTime: z.string().optional(),
    incidentDescription: z.string().optional(),
    timeline: z.array(z.object({
      time: z.string(),
      event: z.string(),
      evidence: z.string().optional(),
    })).default([]),
  }).optional(),

  // Information Gathering - People
  informationGatheringPeople: z.object({
    peopleInvolved: z.array(z.object({
      name: z.string(),
      companyContractor: z.string(),
      roleInvolvement: z.string(),
    })).default([]),
    injuredPersonName: z.string().optional(),
    injuredPersonDateOfBirth: z.date().optional(),
    injuredPersonGender: z.string().optional(),
    injuredPersonNationality: z.string().optional(),
    injuredPersonTradeActivity: z.string().optional(),
    injuredPersonResponsibility: z.string().optional(),
    injuryIllHealthDamage: z.string().optional(),
    peopleActionsInIncident: z.string().optional(),
    physicalMentalBehaviorCulture: z.string().optional(),
    skillCompetenceLevel: z.string().optional(),
    firstRespondersActions: z.string().optional(),
  }).optional(),

  // Information Gathering - Equipment
  informationGatheringEquipment: z.object({
    equipmentList: z.array(z.object({
      name: z.string(),
      type: z.string(),
      description: z.string(),
    })).default([]),
    equipmentWorkingCondition: z.string().optional(),
    equipmentUsage: z.string().optional(),
    equipmentShapeNature: z.string().optional(),
    equipmentDifficultyUnfamiliarity: z.string().optional(),
    safetyControlsImplemented: z.string().optional(),
    safetyEquipmentAdequacy: z.string().optional(),
  }).optional(),

  // Information Gathering - Material
  informationGatheringMaterial: z.object({
    materialList: z.array(z.object({
      name: z.string(),
      type: z.string(),
      description: z.string(),
    })).default([]),
    materialUsage: z.string().optional(),
    materialShapeNatureProperty: z.string().optional(),
    materialDifficultyUnfamiliarity: z.string().optional(),
    safetyControlsManufacturerRequirements: z.string().optional(),
  }).optional(),

  // Information Gathering - Environment
  informationGatheringEnvironment: z.object({
    locationOfIncident: z.string().optional(),
    workingConditionsEnvironment: z.string().optional(),
    maintenanceWorkplaceLayout: z.string().optional(),
    environmentalFeatures: z.string().optional(),
  }).optional(),

  // Information Gathering - Method
  informationGatheringMethod: z.object({
    documentationRelevance: z.string().optional(),
    organizationArrangements: z.string().optional(),
    systemsProcesses: z.string().optional(),
  }).optional(),

  // Conclusions
  investigationConclusions: z.object({
    conclusionRemarks: z.string().optional(),
    evidenceImages: z.array(z.object({
      file: z.any().optional(),
      fileName: z.string().optional(),
      fileSize: z.number().optional(),
      description: z.string().optional(),
    })).default([]),
  }).optional(),

  // Action Controls
  actionControls: z.array(z.object({
    correctiveControlMeasures: z.string(),
    dueDate: z.date(),
    personResponsible: z.string(),
  })).default([]),
});

type ComprehensiveInvestigationFormValues = z.infer<typeof comprehensiveInvestigationSchema>;

interface ComprehensiveInvestigationFormProps {
  incident: any;
  onSubmit: (data: ComprehensiveInvestigationFormValues) => void;
  isReadOnly?: boolean;
  isNested?: boolean;
}

const ComprehensiveInvestigationForm: React.FC<ComprehensiveInvestigationFormProps> = ({
  incident,
  onSubmit,
  isReadOnly = false,
  isNested = false,
}) => {
  // Initialize form with existing data if available
  const defaultValues = incident?.comprehensiveInvestigation || {
    investigationTeam: {
      leadInvestigator: incident?.leadInvestigator || "",
      teamMembers: [],
      investigationStartDate: undefined,
      investigationEndDate: undefined,
      purposeOfInvestigation: "",
      consequenceOfInterest: "",
      timeframeOfInterest: "",
      peopleOfInterest: "",
      equipmentOfInterest: "",
      activitiesOfInterest: "",
      geographicalBoundariesOfInterest: "",
    },
    sequenceOfEvents: {
      description: "",
      incidentDate: undefined,
      incidentTime: "",
      incidentDescription: "",
      timeline: [],
    },
    informationGatheringPeople: {
      peopleInvolved: [],
      injuredPersonName: "",
      injuredPersonDateOfBirth: undefined,
      injuredPersonGender: "",
      injuredPersonNationality: "",
      injuredPersonTradeActivity: "",
      injuredPersonResponsibility: "",
      injuryIllHealthDamage: "",
      peopleActionsInIncident: "",
      physicalMentalBehaviorCulture: "",
      skillCompetenceLevel: "",
      firstRespondersActions: "",
    },
    informationGatheringEquipment: {
      equipmentList: [],
      equipmentWorkingCondition: "",
      equipmentUsage: "",
      equipmentShapeNature: "",
      equipmentDifficultyUnfamiliarity: "",
      safetyControlsImplemented: "",
      safetyEquipmentAdequacy: "",
    },
    informationGatheringMaterial: {
      materialList: [],
      materialUsage: "",
      materialShapeNatureProperty: "",
      materialDifficultyUnfamiliarity: "",
      safetyControlsManufacturerRequirements: "",
    },
    informationGatheringEnvironment: {
      locationOfIncident: "",
      workingConditionsEnvironment: "",
      maintenanceWorkplaceLayout: "",
      environmentalFeatures: "",
    },
    informationGatheringMethod: {
      documentationRelevance: "",
      organizationArrangements: "",
      systemsProcesses: "",
    },
    investigationConclusions: {
      conclusionRemarks: "",
      evidenceImages: [],
    },
    actionControls: [],
  };

  const form = useForm<ComprehensiveInvestigationFormValues>({
    resolver: zodResolver(comprehensiveInvestigationSchema),
    mode: "onBlur",
    defaultValues,
  });

  const handleSubmit = (data: ComprehensiveInvestigationFormValues) => {
    console.log("ComprehensiveInvestigationForm - handleSubmit called with data:", data);
    onSubmit(data);
  };

  // Styling classes
  const formLabelClass = "text-sm font-medium";
  const sectionTitleClass = "text-lg font-semibold mb-4 flex items-center gap-2";
  const sectionIconClass = "w-5 h-5 text-primary";

  // Add event listeners to all form fields to trigger form submission when nested
  React.useEffect(() => {
    if (isNested) {
      // Subscribe to form changes
      const subscription = form.watch(() => {
        console.log("ComprehensiveInvestigationForm - form changed");
        // Get the current form values and submit them to the parent component
        const currentData = form.getValues();
        console.log("ComprehensiveInvestigationForm - current form values:", currentData);
        onSubmit(currentData);
      });

      // Cleanup subscription on unmount
      return () => subscription.unsubscribe();
    }
  }, [form, onSubmit, isNested]);

  // Create the form content
  const formContent = (
    <div className="space-y-6">
      <Accordion type="single" defaultValue="investigation-team" collapsible className="w-full">
        {/* Investigation Team Section */}
        <AccordionItem value="investigation-team">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Users className={sectionIconClass} />
              Investigation Team
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          <FormField
            control={form.control}
            name="investigationTeam.leadInvestigator"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Lead Investigator (Required)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Name of the lead investigator"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Team Members Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Team Members</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentMembers = form.getValues("investigationTeam.teamMembers") || [];
                    form.setValue("investigationTeam.teamMembers", [
                      ...currentMembers,
                      { name: "", designation: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Team Member
                </Button>
              )}
            </div>

            {form.watch("investigationTeam.teamMembers")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Team Member {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentMembers = form.getValues("investigationTeam.teamMembers") || [];
                        const updatedMembers = currentMembers.filter((_, i) => i !== index);
                        form.setValue("investigationTeam.teamMembers", updatedMembers);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`investigationTeam.teamMembers.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Team member name"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`investigationTeam.teamMembers.${index}.designation`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Designation</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Job title/designation"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("investigationTeam.teamMembers") || form.watch("investigationTeam.teamMembers")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No team members added. Click "Add Team Member" to add one.
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="investigationTeam.investigationStartDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel className={formLabelClass}>Investigation Start Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isReadOnly}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.investigationEndDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel className={formLabelClass}>Investigation End Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isReadOnly}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Purpose and Scope Section */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Investigation Scope & Purpose</h3>

            <FormField
              control={form.control}
              name="investigationTeam.purposeOfInvestigation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Purpose of Investigation</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the purpose and objectives of this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.consequenceOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Consequence of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the consequences or outcomes of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.timeframeOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Timeframe of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Specify the timeframe or period of interest for this investigation"
                      className="min-h-[60px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.peopleOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>People of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Identify key people of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.equipmentOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Equipment of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Identify equipment, tools, or machinery of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.activitiesOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Activities of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe activities, processes, or operations of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.geographicalBoundariesOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Geographical Boundaries of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Define the geographical boundaries or locations of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Sequence of Events Section */}
        <AccordionItem value="sequence-of-events">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Clock className={sectionIconClass} />
              Sequence of Events Leading to Damage / Incident
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          {/* Basic Incident Information */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Basic Incident Information</h3>

            <FormField
              control={form.control}
              name="sequenceOfEvents.description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide a brief description of the incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="sequenceOfEvents.incidentDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className={formLabelClass}>Date (dd-mm-yyyy)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={isReadOnly}
                          >
                            {field.value ? (
                              format(field.value, "dd-MM-yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sequenceOfEvents.incidentTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>Time</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 14:30 or 2:30 PM"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="sequenceOfEvents.incidentDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Incident Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide a detailed description of what happened during the incident"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>



          {/* Timeline Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Timeline of Events</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentTimeline = form.getValues("sequenceOfEvents.timeline") || [];
                    form.setValue("sequenceOfEvents.timeline", [
                      ...currentTimeline,
                      { time: "", event: "", evidence: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Timeline Entry
                </Button>
              )}
            </div>

            {form.watch("sequenceOfEvents.timeline")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Timeline Entry {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentTimeline = form.getValues("sequenceOfEvents.timeline") || [];
                        const updatedTimeline = currentTimeline.filter((_, i) => i !== index);
                        form.setValue("sequenceOfEvents.timeline", updatedTimeline);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.time`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Time</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., 09:30 AM"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.event`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Event</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Describe what happened"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.evidence`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Evidence</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Supporting evidence"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("sequenceOfEvents.timeline") || form.watch("sequenceOfEvents.timeline")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No timeline entries added. Click "Add Timeline Entry" to add one.
              </div>
            )}
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Information Gathering - People Section */}
        <AccordionItem value="information-gathering-people">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Users className={sectionIconClass} />
              Information Gathering (A. People)
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          {/* People Involved Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>People Involved</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentPeople = form.getValues("informationGatheringPeople.peopleInvolved") || [];
                    form.setValue("informationGatheringPeople.peopleInvolved", [
                      ...currentPeople,
                      { name: "", companyContractor: "", roleInvolvement: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Person
                </Button>
              )}
            </div>

            {form.watch("informationGatheringPeople.peopleInvolved")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Person {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentPeople = form.getValues("informationGatheringPeople.peopleInvolved") || [];
                        const updatedPeople = currentPeople.filter((_, i) => i !== index);
                        form.setValue("informationGatheringPeople.peopleInvolved", updatedPeople);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name={`informationGatheringPeople.peopleInvolved.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Person's name"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringPeople.peopleInvolved.${index}.companyContractor`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Company / Contractor</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Company or contractor name"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringPeople.peopleInvolved.${index}.roleInvolvement`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Role / Involvement</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Role or involvement in incident"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("informationGatheringPeople.peopleInvolved") || form.watch("informationGatheringPeople.peopleInvolved")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No people added. Click "Add Person" to add one.
              </div>
            )}
          </div>

          {/* Injured Person Details */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Injured Person Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>A1. Name of Injured Person?</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Name of the injured person"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonDateOfBirth"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className={formLabelClass}>A1. Date of Birth?</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={isReadOnly}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonGender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>A1. Gender</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Gender"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonNationality"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>A1. Nationality</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nationality"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonTradeActivity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>A1. Trade Activity</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Trade or activity"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="informationGatheringPeople.injuredPersonResponsibility"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>A1. Responsibility</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Responsibility or role"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Analysis Questions */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Analysis Questions</h3>

            <FormField
              control={form.control}
              name="informationGatheringPeople.injuryIllHealthDamage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>A2. What injury, ill-health or damage was caused?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the injury, ill-health or damage that was caused"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringPeople.peopleActionsInIncident"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>A3. What did the people involved in the incident do/not do that was essential to continuing the incident sequence?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what people did or did not do that contributed to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringPeople.physicalMentalBehaviorCulture"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>A4. Consider - Physical capability or condition / Mental state / Behaviour / Culture or custom.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Consider physical capability, mental state, behavior, culture or customs that may have contributed"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringPeople.skillCompetenceLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>A5. Consider - Skill or competence level.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Consider the skill or competence level of people involved"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringPeople.firstRespondersActions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>A6. Who were the first responders and what actions were taken?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe who the first responders were and what actions they took"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Information Gathering - Equipment Section */}
        <AccordionItem value="information-gathering-equipment">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Wrench className={sectionIconClass} />
              Information Gathering (B. Equipment)
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          {/* Equipment List Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <Label className={formLabelClass}>Equipment Elements Involved</Label>
                <p className="text-sm text-gray-600 mt-1">
                  List the 'Equipment' elements involved in the immediate circumstances of the incident - plant, machinery, equipment, tools, PPE, etc.
                </p>
              </div>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentEquipment = form.getValues("informationGatheringEquipment.equipmentList") || [];
                    form.setValue("informationGatheringEquipment.equipmentList", [
                      ...currentEquipment,
                      { name: "", type: "", description: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Equipment
                </Button>
              )}
            </div>

            {form.watch("informationGatheringEquipment.equipmentList")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Equipment {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentEquipment = form.getValues("informationGatheringEquipment.equipmentList") || [];
                        const updatedEquipment = currentEquipment.filter((_, i) => i !== index);
                        form.setValue("informationGatheringEquipment.equipmentList", updatedEquipment);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name={`informationGatheringEquipment.equipmentList.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Equipment Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name of equipment"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringEquipment.equipmentList.${index}.type`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Type</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Plant, Machinery, Tool, PPE"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringEquipment.equipmentList.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Description</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Brief description"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("informationGatheringEquipment.equipmentList") || form.watch("informationGatheringEquipment.equipmentList")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No equipment added. Click "Add Equipment" to add one.
              </div>
            )}
          </div>

          {/* Analysis Questions */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Equipment Analysis Questions</h3>

            <FormField
              control={form.control}
              name="informationGatheringEquipment.equipmentWorkingCondition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B1. Were the equipment in good working condition?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the working condition of the equipment involved"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEquipment.equipmentUsage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B2. How were the equipment used?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe how the equipment were used during the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEquipment.equipmentShapeNature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B3. Was the shape / nature of the equipment relevant to the incident? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe if the shape or nature of the equipment was relevant to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEquipment.equipmentDifficultyUnfamiliarity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B4. Was difficulty / unfamiliarity in using the equipment, etc. a contributory factor? If "Yes", describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe any difficulty or unfamiliarity with equipment that contributed to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEquipment.safetyControlsImplemented"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B5. Were the required safety controls implemented to address potential risks from the equipment? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the safety controls that were or were not implemented for the equipment"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEquipment.safetyEquipmentAdequacy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>B6. Was safety equipment (e.g., PPE) adequate to operate the equipment in a safe manner? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the adequacy of safety equipment (PPE, etc.) for safe operation"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Information Gathering - Material Section */}
        <AccordionItem value="information-gathering-material">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Package className={sectionIconClass} />
              Information Gathering (C. Material)
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          {/* Material List Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <Label className={formLabelClass}>Material Elements Involved</Label>
                <p className="text-sm text-gray-600 mt-1">
                  List the 'Material' elements involved in the immediate circumstances of the incident
                </p>
              </div>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentMaterials = form.getValues("informationGatheringMaterial.materialList") || [];
                    form.setValue("informationGatheringMaterial.materialList", [
                      ...currentMaterials,
                      { name: "", type: "", description: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Material
                </Button>
              )}
            </div>

            {form.watch("informationGatheringMaterial.materialList")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Material {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentMaterials = form.getValues("informationGatheringMaterial.materialList") || [];
                        const updatedMaterials = currentMaterials.filter((_, i) => i !== index);
                        form.setValue("informationGatheringMaterial.materialList", updatedMaterials);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name={`informationGatheringMaterial.materialList.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Material Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name of material"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringMaterial.materialList.${index}.type`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Type</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Chemical, Raw Material, Product"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringMaterial.materialList.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Description</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Brief description"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("informationGatheringMaterial.materialList") || form.watch("informationGatheringMaterial.materialList")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No materials added. Click "Add Material" to add one.
              </div>
            )}
          </div>

          {/* Analysis Questions */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Material Analysis Questions</h3>

            <FormField
              control={form.control}
              name="informationGatheringMaterial.materialUsage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>C1. How were the materials used?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe how the materials were used during the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringMaterial.materialShapeNatureProperty"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>C2. Was the shape / nature / property of the materials relevant to the incident? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe if the shape, nature, or properties of the materials were relevant to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringMaterial.materialDifficultyUnfamiliarity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>C3. Was difficulty / unfamiliarity in handling the materials, etc. a contributory factor? If "Yes", describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe any difficulty or unfamiliarity with material handling that contributed to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringMaterial.safetyControlsManufacturerRequirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>C4. Were there any safety controls required as per manufacturer's requirements and were they implemented? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the manufacturer's safety requirements and whether they were implemented"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Information Gathering - Environment Section */}
        <AccordionItem value="information-gathering-environment">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Cloud className={sectionIconClass} />
              Information Gathering (D. Environment)
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          <div className="space-y-4">
            <div>
              <Label className={formLabelClass}>Environment Elements Involved</Label>
              <p className="text-sm text-gray-600 mt-1">
                List the 'Environment' elements involved in the immediate circumstances of the incident
              </p>
            </div>
          </div>

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.locationOfIncident"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>D1. Location of the incident on the project / operation: What were the positions of all parties (injured party / witnesses), any machinery, materials, barriers, signs, protections, tools & equipment, etc.? (Provide plan)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the location of the incident and positions of all parties, machinery, materials, barriers, signs, protections, tools & equipment. Provide a plan if possible."
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.workingConditionsEnvironment"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>D2. Were there anything unusual about the working conditions and work environment? Describe.</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any unusual working conditions or work environment factors that may have contributed to the incident"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.maintenanceWorkplaceLayout"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>D3. Were maintenance, workplace layout and/or housekeeping relevant factors? If so, what were they?</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any maintenance issues, workplace layout problems, or housekeeping factors that were relevant to the incident"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.environmentalFeatures"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>D4. What other features of the environment were present/absent that was essential to contribute to the incident occurring? Describe.</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe other environmental features that were present or absent and essential to the incident occurring"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Information Gathering - Method Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <FileText className={sectionIconClass} />
            Information Gathering (E. Documents)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label className={formLabelClass}>Documents Elements Involved</Label>
              <p className="text-sm text-gray-600 mt-1">
                List the 'Documents' elements involved in the immediate circumstances of the incident
              </p>
            </div>
          </div>

          <FormField
            control={form.control}
            name="informationGatheringMethod.documentationRelevance"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>E1. What documentation is relevant and are they adequate? Describe. Paper evidence includes all relevant documentation e.g., risk assessments and risk registers / JSA or safety method statements / EHS plans / drawings / instructions / permits / certification (test, examination, training), licenses / induction & toolbox talk registers.</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe what documentation is relevant and whether it is adequate. Include risk assessments, risk registers, JSA, safety method statements, EHS plans, drawings, instructions, permits, certifications (test, examination, training), licenses, induction & toolbox talk registers."
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.organizationArrangements"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>E2. Were the organisation and arrangements for the works contributory factors? Describe.</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe whether the organization and arrangements for the works were contributory factors to the incident"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.systemsProcesses"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>E3. What other aspects of Systems and Processes were contributory to the incident occurring? Describe.</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe other aspects of systems and processes that were contributory to the incident occurring"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Information Gathering - Method Section */}
        <AccordionItem value="information-gathering-method">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Settings className={sectionIconClass} />
              Information Gathering (E. Documents)
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          <div className="space-y-4">
            <div>
              <Label className={formLabelClass}>Documents and Systems Analysis</Label>
              <p className="text-sm text-gray-600 mt-1">
                Analyze documentation, organizational arrangements, and systems that may have contributed to the incident
              </p>
            </div>

            <FormField
              control={form.control}
              name="informationGatheringMethod.documentationRelevance"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>E1. What documentation was relevant and adequate? (Risk assessments, JSA, EHS plans, drawings, permits, certifications, licenses, induction records)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the documentation that was relevant and assess its adequacy"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringMethod.organizationArrangements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>E2. What aspects of organization and work arrangements were contributory factors to the incident occurring? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe organizational and work arrangement factors that contributed to the incident"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringMethod.systemsProcesses"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>E3. What other aspects of Systems and Processes were contributory to the incident occurring? Describe.</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe other aspects of systems and processes that were contributory to the incident occurring"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Conclusions Section */}
        <AccordionItem value="conclusions">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <FileText className={sectionIconClass} />
              Conclusions of Investigation Team
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-6 pt-6">
          <FormField
            control={form.control}
            name="investigationConclusions.conclusionRemarks"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Conclusion Remarks</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Provide the conclusion remarks from the investigation team"
                    className="min-h-[150px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Evidence Images Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <Label className={formLabelClass}>Evidence Images</Label>
                <p className="text-sm text-gray-600 mt-1">
                  Upload images that serve as evidence for the investigation conclusions
                </p>
              </div>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentImages = form.getValues("investigationConclusions.evidenceImages") || [];
                    form.setValue("investigationConclusions.evidenceImages", [
                      ...currentImages,
                      { file: null, fileName: "", fileSize: 0, description: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Upload className="w-4 h-4 mr-1" />
                  Add Evidence Image
                </Button>
              )}
            </div>

            {form.watch("investigationConclusions.evidenceImages")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium flex items-center gap-2">
                    <Image className="w-4 h-4" />
                    Evidence Image {index + 1}
                  </h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentImages = form.getValues("investigationConclusions.evidenceImages") || [];
                        const updatedImages = currentImages.filter((_, i) => i !== index);
                        form.setValue("investigationConclusions.evidenceImages", updatedImages);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="space-y-3">
                  {/* File Upload */}
                  <div>
                    <Label className="text-xs">Upload Image</Label>
                    <div className="mt-1">
                      <Input
                        type="file"
                        accept="image/*"
                        disabled={isReadOnly}
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const currentImages = form.getValues("investigationConclusions.evidenceImages") || [];
                            const updatedImages = [...currentImages];
                            updatedImages[index] = {
                              ...updatedImages[index],
                              file: file,
                              fileName: file.name,
                              fileSize: file.size
                            };
                            form.setValue("investigationConclusions.evidenceImages", updatedImages);
                          }
                        }}
                        className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                    {form.watch(`investigationConclusions.evidenceImages.${index}.fileName`) && (
                      <div className="mt-2 text-sm text-gray-600">
                        <span className="font-medium">File:</span> {form.watch(`investigationConclusions.evidenceImages.${index}.fileName`)}
                        {form.watch(`investigationConclusions.evidenceImages.${index}.fileSize`) && (
                          <span className="ml-2">
                            ({Math.round((form.watch(`investigationConclusions.evidenceImages.${index}.fileSize`) || 0) / 1024)} KB)
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Image Description */}
                  <FormField
                    control={form.control}
                    name={`investigationConclusions.evidenceImages.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Image Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe what this evidence image shows and its relevance to the investigation"
                            className="min-h-[80px]"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("investigationConclusions.evidenceImages") || form.watch("investigationConclusions.evidenceImages")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No evidence images added. Click "Add Evidence Image" to upload supporting evidence.
              </div>
            )}
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Assign Action Controls Section */}
        <AccordionItem value="action-controls">
          <AccordionTrigger className="text-lg font-semibold hover:no-underline">
            <div className="flex items-center gap-2">
              <Target className={sectionIconClass} />
              Assign Action Controls
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Card>
              <CardContent className="space-y-4 pt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Corrective/Control measures</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentActions = form.getValues("actionControls") || [];
                    form.setValue("actionControls", [
                      ...currentActions,
                      {
                        correctiveControlMeasures: "",
                        dueDate: new Date(),
                        personResponsible: ""
                      }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Corrective/Control Measure
                </Button>
              )}
            </div>

            {form.watch("actionControls")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Corrective/Control Measure {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentActions = form.getValues("actionControls") || [];
                        const updatedActions = currentActions.filter((_, i) => i !== index);
                        form.setValue("actionControls", updatedActions);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name={`actionControls.${index}.correctiveControlMeasures`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={formLabelClass}>Corrective/Control measures</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the corrective or control measures to be implemented"
                          className="min-h-[100px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.dueDate`}
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel className={formLabelClass}>Due Date (dd-mm-yyyy)</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                                disabled={isReadOnly}
                              >
                                {field.value ? (
                                  format(field.value, "dd-MM-yyyy")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.personResponsible`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={formLabelClass}>Person Responsible</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isReadOnly}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select person responsible" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Project Manager">Project Manager</SelectItem>
                            <SelectItem value="Safety Officer">Safety Officer</SelectItem>
                            <SelectItem value="Site Supervisor">Site Supervisor</SelectItem>
                            <SelectItem value="Team Lead">Team Lead</SelectItem>
                            <SelectItem value="Engineer">Engineer</SelectItem>
                            <SelectItem value="Contractor">Contractor</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("actionControls") || form.watch("actionControls")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No corrective/control measures added. Click "Add Corrective/Control Measure" to add one.
              </div>
            )}
          </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Submit Button */}
      {!isReadOnly && !isNested && (
        <div className="flex justify-end pt-6">
          <Button type="submit" className="bg-green-600 hover:bg-green-700 text-white px-8">
            <CheckCircle className="w-4 h-4 mr-2" />
            Submit Comprehensive Investigation
          </Button>
        </div>
      )}
    </div>
  );

  // Return different structures based on whether this is nested in another form
  return isNested ? (
    formContent
  ) : (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {formContent}
      </form>
    </Form>
  );
};

export default ComprehensiveInvestigationForm;
