
import { useState, useRef } from "react";
import { Camera, Image, X, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { ApiService } from "@/lib/utils";

interface PhotoUploadProps {
  onPhotosChange: (uploads: string[]) => void;
  existingPhotos?: string[];
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  onPhotosChange,
  existingPhotos = []
}) => {
  const [uploads, setUploads] = useState<string[]>(existingPhotos);
  const [previews, setPreviews] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      if (uploads.length + newFiles.length > 5) {
        toast.error("Maximum 5 photos allowed");
        return;
      }

      setIsUploading(true);

      try {
        console.log('🔄 Uploading files:', newFiles);

        // Generate previews immediately for better UX
        const newPreviews = newFiles.map(file => URL.createObjectURL(file));
        setPreviews(prev => [...prev, ...newPreviews]);

        // Upload files to /files API
        const uploadResponse = await ApiService.uploadFiles(newFiles);
        console.log('📤 Upload response:', uploadResponse);

        // Extract originalname from the response
        const uploadedFileNames = uploadResponse.files.map((file: any) => file.originalname);
        console.log('📁 Uploaded file names:', uploadedFileNames);

        // Update uploads array with the original file names
        const updatedUploads = [...uploads, ...uploadedFileNames];
        setUploads(updatedUploads);
        onPhotosChange(updatedUploads);

        toast.success(`Successfully uploaded ${newFiles.length} photo(s)`);
      } catch (error) {
        console.error('❌ Upload failed:', error);
        toast.error('Failed to upload photos. Please try again.');

        // Remove the preview images since upload failed
        const failedPreviews = newFiles.map(file => URL.createObjectURL(file));
        failedPreviews.forEach(preview => URL.revokeObjectURL(preview));
        setPreviews(prev => prev.slice(0, prev.length - newFiles.length));
      } finally {
        setIsUploading(false);
      }
    }
  };

  const removePhoto = (index: number) => {
    const updatedUploads = uploads.filter((_, i) => i !== index);
    setUploads(updatedUploads);
    onPhotosChange(updatedUploads);

    // Revoke the URL to avoid memory leaks
    URL.revokeObjectURL(previews[index]);
    setPreviews(prev => prev.filter((_, i) => i !== index));
  };

  const triggerFileInput = (source: 'camera' | 'library') => {
    if (isUploading) {
      toast.info("Please wait for current upload to complete");
      return;
    }

    if (fileInputRef.current) {
      fileInputRef.current.accept = source === 'camera'
        ? 'image/*;capture=camera'
        : 'image/*';
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          type="button"
          onClick={() => triggerFileInput('camera')}
          className="flex items-center gap-2"
          disabled={isUploading}
        >
          {isUploading ? <Upload size={18} className="animate-spin" /> : <Camera size={18} />}
          {isUploading ? 'Uploading...' : 'Take Photo'}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => triggerFileInput('library')}
          className="flex items-center gap-2"
          disabled={isUploading}
        >
          {isUploading ? <Upload size={18} className="animate-spin" /> : <Image size={18} />}
          {isUploading ? 'Uploading...' : 'Choose from Library'}
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          hidden
          accept="image/*"
          onChange={handleFileChange}
          multiple
        />
      </div>
      
      {previews.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-4">
          {previews.map((preview, index) => (
            <div key={index} className="relative">
              <img
                src={preview}
                alt={`Incident photo ${index + 1}`}
                className="w-full h-48 object-cover rounded-md"
              />
              <button
                type="button"
                onClick={() => removePhoto(index)}
                className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md"
              >
                <X size={16} className="text-red-500" />
              </button>
            </div>
          ))}
        </div>
      )}
      <p className="text-xs text-gray-500">
        Upload up to 5 photos. Clear images help with incident investigation.
        {uploads.length > 0 && (
          <span className="block mt-1 text-green-600">
            {uploads.length} photo(s) uploaded: {uploads.join(', ')}
          </span>
        )}
      </p>
    </div>
  );
};

export default PhotoUpload;
