import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService } from '@/lib/utils';
import { toast } from 'sonner';

const AllIncidentsApiTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [incidents, setIncidents] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAllIncidentsEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setIncidents(null);

    try {
      console.log('🔄 Testing /all-new-report-incidents endpoint...');
      const response = await ApiService.getAllIncidents();
      console.log('✅ All incidents test successful:', response);
      setIncidents(response);
      toast.success('All incidents loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ All incidents test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load incidents: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testLegacyEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setIncidents(null);

    try {
      console.log('🔄 Testing legacy getIncidents endpoint...');
      const response = await ApiService.getIncidents();
      console.log('✅ Legacy incidents test successful:', response);
      setIncidents(response);
      toast.success('Incidents loaded successfully via legacy method!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Legacy incidents test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load incidents: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          All Incidents API Test
          <div className="flex gap-2">
            <Button 
              onClick={testAllIncidentsEndpoint} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Loading...' : 'Test New Endpoint'}
            </Button>
            <Button 
              onClick={testLegacyEndpoint} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Loading...' : 'Test Legacy Method'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">New Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/all-new-report-incidents?filter=...
          </code>
        </div>

        <div>
          <h3 className="font-medium mb-2">Filter Parameters:</h3>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
{`{
  "include": [
    "locationOne", "locationTwo", "locationThree",
    "locationFour", "locationFive", "locationSix",
    "incidentCircumstanceCategory", "incidentCircumstanceDescription",
    "incidentCircumstanceType", "lighting", "riskCategory",
    "surfaceCondition", "surfaceType", "workActivity",
    "reviewer", "user"
  ]
}`}
          </pre>
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
            <strong>Note:</strong> Version filtering (version: "new") is applied client-side after receiving the response.
          </div>
        </div>

        {isLoading && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="text-blue-700">🔄 Loading incidents...</div>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded">
            <div className="text-red-700 font-medium">❌ Error:</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
          </div>
        )}

        {incidents && (
          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <div className="text-green-700 font-medium mb-2">✅ Success! Incidents Response:</div>
            
            {/* Show incident count and summary */}
            {Array.isArray(incidents) && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-medium text-blue-800 mb-2">Incidents Summary:</h4>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="font-medium">Total Incidents:</span>
                    <span className="text-blue-600 ml-2">{incidents.length}</span>
                  </div>
                  {incidents.length > 0 && (
                    <div>
                      <span className="font-medium">Sample Incident Fields:</span>
                      <div className="mt-1 text-xs bg-white px-2 py-1 rounded border">
                        {Object.keys(incidents[0]).join(', ')}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="text-sm text-gray-600 mb-2">Full API Response:</div>
            <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-96">
              {JSON.stringify(incidents, null, 2)}
            </pre>
          </div>
        )}

        {!incidents && !isLoading && !error && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Click "Test New Endpoint" to test the /all-new-report-incidents API</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AllIncidentsApiTest;
