import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService } from '@/lib/utils';

const ApiTest = () => {
  const [userData, setUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApiConnection = async () => {
    setIsLoading(true);
    setError(null);
    setUserData(null);

    try {
      console.log('Testing API connection...');
      const data = await ApiService.getCurrentUser();
      console.log('API test successful:', data);
      setUserData(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('API test failed:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          API Connection Test
          <Button 
            onClick={testApiConnection} 
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Testing...' : 'Test API'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/users/me
          </code>
        </div>

        <div>
          <h3 className="font-medium mb-2">Authorization:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block break-all">
            Bearer eyJraWQiOiJQYVRIeXNneEJKSmRlUnZ3cDhMXC9NTGM2UjRiZStGWEVyKytRN3pBMExvdz0iLCJhbGciOiJSUzI1NiJ9...
          </code>
        </div>

        {isLoading && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="text-blue-700">🔄 Making API request...</div>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded">
            <div className="text-red-700 font-medium">❌ Error:</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
          </div>
        )}

        {userData && (
          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <div className="text-green-700 font-medium mb-2">✅ Success! API Response:</div>

            {/* Show validation roles analysis */}
            {userData.validationRoles && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-medium text-blue-800 mb-2">Validation Roles Analysis:</h4>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="font-medium">Has Incident Reporter:</span>
                    <span className={userData.validationRoles.some((role: any) => role.name === 'Incident Reporter') ? 'text-green-600' : 'text-red-600'}>
                      {userData.validationRoles.some((role: any) => role.name === 'Incident Reporter') ? ' ✅ Yes' : ' ❌ No'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Has Incident Reviewer:</span>
                    <span className={userData.validationRoles.some((role: any) => role.name === 'Incident Reviewer') ? 'text-green-600' : 'text-red-600'}>
                      {userData.validationRoles.some((role: any) => role.name === 'Incident Reviewer') ? ' ✅ Yes' : ' ❌ No'}
                    </span>
                  </div>
                  <div className="mt-2">
                    <span className="font-medium">All Roles ({userData.validationRoles.length}):</span>
                    <div className="mt-1 max-h-32 overflow-y-auto">
                      {userData.validationRoles.map((role: any, index: number) => (
                        <div key={index} className="text-xs bg-white px-2 py-1 rounded border mb-1">
                          {role.name}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="text-sm text-gray-600 mb-2">Full API Response:</div>
            <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-96">
              {JSON.stringify(userData, null, 2)}
            </pre>
          </div>
        )}

        {!userData && !isLoading && !error && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Click "Test API" to make a request to the endpoint</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ApiTest;
