
// Incident Types based on the new requirements
export const incidentTypes = [
  { value: "health", label: "Health" },
  { value: "safety", label: "Safety" }
];

// Original categories kept for reference
export const incidentCategories = [
  { value: "fire", label: "Fire" },
  { value: "slip", label: "Slip" },
  { value: "fall", label: "Fall" },
  { value: "electrical", label: "Electrical Hazard" },
  { value: "chemical", label: "Chemical Spill" },
  { value: "vehicle", label: "Vehicle Accident" },
  { value: "machinery", label: "Machinery" },
  { value: "tool", label: "Tool Related" },
  { value: "ergonomic", label: "Ergonomic" },
  { value: "other", label: "Other" }
];

// Location data
export const countries = [
  { value: "singapore", label: "Singapore" },
  { value: "malaysia", label: "Malaysia" },
  { value: "philippines", label: "Philippines" },
  { value: "thailand", label: "Thailand" },
  { value: "indonesia", label: "Indonesia" },
  { value: "other", label: "Other" }
];

export const cities = {
  singapore: [
    { value: "singapore_city", label: "Singapore City" },
    { value: "jurong", label: "Jurong" },
    { value: "changi", label: "Changi" },
    { value: "woodlands", label: "Woodlands" },
    { value: "other", label: "Other" }
  ],
  malaysia: [
    { value: "kuala_lumpur", label: "Kuala Lumpur" },
    { value: "johor_bahru", label: "Johor Bahru" },
    { value: "penang", label: "Penang" },
    { value: "kota_kinabalu", label: "Kota Kinabalu" },
    { value: "other", label: "Other" }
  ],
  philippines: [
    { value: "manila", label: "Manila" },
    { value: "cebu", label: "Cebu" },
    { value: "davao", label: "Davao" },
    { value: "quezon_city", label: "Quezon City" },
    { value: "other", label: "Other" }
  ],
  thailand: [
    { value: "bangkok", label: "Bangkok" },
    { value: "chiang_mai", label: "Chiang Mai" },
    { value: "phuket", label: "Phuket" },
    { value: "pattaya", label: "Pattaya" },
    { value: "other", label: "Other" }
  ],
  indonesia: [
    { value: "jakarta", label: "Jakarta" },
    { value: "surabaya", label: "Surabaya" },
    { value: "bandung", label: "Bandung" },
    { value: "medan", label: "Medan" },
    { value: "other", label: "Other" }
  ],
  other: [
    { value: "other", label: "Other" }
  ]
};

export const businessUnits = [
  { value: "datacenter", label: "Data Center" },
  { value: "corporate", label: "Corporate" },
  { value: "operations", label: "Operations" },
  { value: "maintenance", label: "Maintenance" },
  { value: "security", label: "Security" },
  { value: "other", label: "Other" }
];

export const projectOptions = [
  { value: "dc1", label: "DC Ops 1" },
  { value: "dc2", label: "DC Ops 2" },
  { value: "dc3", label: "DC Ops 3" },
  { value: "project1", label: "Project Alpha" },
  { value: "project2", label: "Project Beta" },
  { value: "project3", label: "Project Gamma" },
  { value: "other", label: "Other" }
];

// Original workplace activities kept for reference
export const workplaceActivities = [
  { value: "maintenance", label: "Maintenance" },
  { value: "operation", label: "Operation" },
  { value: "construction", label: "Construction" },
  { value: "inspection", label: "Inspection" },
  { value: "office", label: "Office Work" },
  { value: "transportation", label: "Transportation" },
  { value: "other", label: "Other" }
];

export const riskCategories = [
  { value: "highVoltage", label: "High Voltage" },
  { value: "confinedSpace", label: "Confined Space" },
  { value: "heights", label: "Working at Heights" },
  { value: "heavyLoads", label: "Heavy Loads" },
  { value: "hotWork", label: "Hot Work" },
  { value: "hazardousMaterials", label: "Hazardous Materials" },
  { value: "other", label: "Other" }
];

// Yes/No options
export const yesNoOptions = [
  { value: "yes", label: "Yes" },
  { value: "no", label: "No" }
];

// Original impact options kept for reference
export const impactOptions = [
  { value: "yes", label: "Yes" },
  { value: "potential", label: "No, but it could have" },
  { value: "no", label: "No" }
];

// Incident reviewers
export const incidentReviewers = [
  { value: "safety_manager", label: "John Smith - Safety Manager" },
  { value: "hse_coordinator", label: "Maria Rodriguez - HSE Coordinator" },
  { value: "safety_officer", label: "David Chen - Safety Officer" },
  { value: "quality_manager", label: "Sarah Johnson - Quality Manager" },
  { value: "compliance_director", label: "Robert Williams - Compliance Director" }
];

// Lead Investigators
export const leadInvestigators = [
  { value: "lead_investigator_1", label: "Michael Thompson - Lead Safety Investigator" },
  { value: "lead_investigator_2", label: "Sarah Davis - Senior Investigation Officer" },
  { value: "lead_investigator_3", label: "James Wilson - Principal Safety Analyst" },
  { value: "lead_investigator_4", label: "Emily Brown - Investigation Team Lead" },
  { value: "lead_investigator_5", label: "Robert Garcia - Chief Investigation Officer" },
  { value: "lead_investigator_6", label: "Lisa Martinez - Senior Safety Investigator" }
];


