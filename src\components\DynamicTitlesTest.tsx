import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService } from '@/lib/utils';
import { toast } from 'sonner';

const DynamicTitlesTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [dynamicTitles, setDynamicTitles] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDynamicTitles = async () => {
    setIsLoading(true);
    setError(null);
    setDynamicTitles(null);

    try {
      console.log('🔄 Testing dynamic titles API...');
      const response = await ApiService.getDynamicTitles();
      console.log('✅ Dynamic titles test successful:', response);
      setDynamicTitles(response);
      toast.success('Dynamic titles loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Dynamic titles test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load dynamic titles: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Dynamic Titles API Test
          <Button 
            onClick={testDynamicTitles} 
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Test Dynamic Titles'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/dynamic-titles
          </code>
        </div>

        <div>
          <h3 className="font-medium mb-2">Expected Response Format:</h3>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
{`[
  {
    "id": "64515477d89318a21a2e466b",
    "title": "LocationOne",
    "altTitle": "Country"
  },
  {
    "id": "64515481d89318a21a2e466c", 
    "title": "LocationTwo",
    "altTitle": "City"
  },
  ...
]`}
          </pre>
        </div>

        {isLoading && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="text-blue-700">🔄 Loading dynamic titles...</div>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded">
            <div className="text-red-700 font-medium">❌ Error:</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
          </div>
        )}

        {dynamicTitles && (
          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <div className="text-green-700 font-medium mb-2">✅ Success! Dynamic Titles Response:</div>
            
            {/* Show location mapping */}
            {Array.isArray(dynamicTitles) && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-medium text-blue-800 mb-2">Location Label Mapping:</h4>
                <div className="space-y-1 text-sm">
                  {dynamicTitles.map((title: any, index: number) => (
                    <div key={index} className="flex justify-between items-center bg-white px-2 py-1 rounded border">
                      <span className="font-medium">{title.title}:</span>
                      <span className="text-blue-600">{title.altTitle}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="text-sm text-gray-600 mb-2">Full API Response:</div>
            <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-96">
              {JSON.stringify(dynamicTitles, null, 2)}
            </pre>
          </div>
        )}

        {!dynamicTitles && !isLoading && !error && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Click "Test Dynamic Titles" to load location labels from the API</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DynamicTitlesTest;
