
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import SimpleLayout from "./components/layout/SimpleLayout";
import Index from "./pages/Index";
import Incidents from "./pages/Incidents";
import NotFound from "./pages/NotFound";
import ReporterDashboard from "./pages/ReporterDashboard";
import ReviewerDashboard from "./pages/ReviewerDashboard";
import ApiTestPage from "./pages/ApiTestPage";

import { UserProvider, useUser } from "./contexts/UserContext";
import { IncidentProvider } from "./contexts/IncidentContext";
import { defaultPath } from "./lib/paths";
import "./App.css";

// Route protection component
const ProtectedRoute = ({ children, requiredRole }: { children: React.ReactNode; requiredRole: 'reporter' | 'reviewer' }) => {
  const { hasIncidentReporter, hasIncidentReviewer, isLoading, userData } = useUser();

  // Show loading while fetching user data
  if (isLoading || !userData || !userData.validationRoles) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Verifying permissions...</p>
        </div>
      </div>
    );
  }

  // Check if user has required role
  const hasRequiredRole = requiredRole === 'reviewer' ? hasIncidentReviewer : hasIncidentReporter;

  if (!hasRequiredRole) {
    console.log(`🚫 Access denied: User doesn't have ${requiredRole} role. Redirecting...`);

    // Redirect to appropriate dashboard based on user's actual roles
    if (hasIncidentReviewer) {
      return <Navigate to={defaultPath.reviewer} replace />;
    } else if (hasIncidentReporter) {
      return <Navigate to={defaultPath.reporter} replace />;
    } else {
      // No incident roles - redirect to reporter as fallback
      return <Navigate to={defaultPath.reporter} replace />;
    }
  }

  console.log(`✅ Access granted: User has ${requiredRole} role`);
  return <>{children}</>;
};

const queryClient = new QueryClient();

// Component to handle role-based redirection
const RoleBasedRedirect = () => {
  const { availableRoles, hasIncidentReporter, hasIncidentReviewer, isLoading, userData } = useUser();

  // Debug logging
  console.log('🔄 RoleBasedRedirect - Debug Info:', {
    isLoading,
    userData: userData,
    availableRoles,
    hasIncidentReporter,
    hasIncidentReviewer,
    validationRoles: userData?.validationRoles
  });

  // Show loading while fetching user data OR if userData is not yet available
  if (isLoading || !userData || !userData.validationRoles) {
    console.log('⏳ Still loading user data...', { isLoading, userData: !!userData, hasValidationRoles: !!userData?.validationRoles });
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading user data...</p>
        </div>
      </div>
    );
  }

  // Redirect based on available roles
  if (availableRoles.length === 1) {
    // User has only one role - redirect directly
    if (hasIncidentReviewer) {
      console.log('✅ Redirecting to reviewer (single role)');
      return <Navigate to={defaultPath.reviewer} replace />;
    }
    if (hasIncidentReporter) {
      console.log('✅ Redirecting to reporter (single role)');
      return <Navigate to={defaultPath.reporter} replace />;
    }
  }

  // User has multiple roles - prioritize reviewer
  if (hasIncidentReviewer) {
    console.log('✅ Redirecting to reviewer (multiple roles - reviewer priority)');
    return <Navigate to={defaultPath.reviewer} replace />;
  }
  if (hasIncidentReporter) {
    console.log('✅ Redirecting to reporter (multiple roles - fallback)');
    return <Navigate to={defaultPath.reporter} replace />;
  }

  // Fallback - no incident roles, redirect to reporter dashboard
  console.log('⚠️ No incident roles found - redirecting to reporter (fallback)');
  return <Navigate to={defaultPath.reporter} replace />;
};

const App = () => (
  <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
    <QueryClientProvider client={queryClient}>
      <UserProvider>
        <IncidentProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <SimpleLayout>
                <Routes>
                  {/* Role-based redirect for root path */}
                  <Route path="/" element={<RoleBasedRedirect />} />

                  {/* Role-specific dashboards with protection */}
                  <Route
                    path={defaultPath.reporter}
                    element={
                      <ProtectedRoute requiredRole="reporter">
                        <ReporterDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path={defaultPath.reviewer}
                    element={
                      <ProtectedRoute requiredRole="reviewer">
                        <ReviewerDashboard />
                      </ProtectedRoute>
                    }
                  />

                  {/* Legacy routes */}
                  <Route path={defaultPath.incidents} element={<Incidents />} />
                  <Route path={defaultPath.report} element={<Index />} />

                  {/* API Test route */}
                  <Route path="/api-test" element={<ApiTestPage />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </SimpleLayout>
            </BrowserRouter>
          </TooltipProvider>
        </IncidentProvider>
      </UserProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;
